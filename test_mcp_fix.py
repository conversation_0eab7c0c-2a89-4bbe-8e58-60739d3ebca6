#!/usr/bin/env python3
"""
MCP Integration Test Script

Bu script, BlenderGPT eklentisindeki MCP entegrasyonu sorunlarının 
düzeltilip düzeltilmediğini test eder.

Author: inkbytefo
"""

import sys
import os
import logging

# Logging ayarları
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_mcp_imports():
    """MCP modüllerinin import edilebilirliğini test eder."""
    print("=== MCP Import Testi ===")
    
    try:
        # MCP modüllerini import et
        from mcp.server_manager import MCPServerManager, MCPServerConfig
        from mcp.client import MCPClientManager
        from mcp.json_config import MCPJSONConfigManager
        print("✅ Tüm MCP modülleri başarıyla import edildi")
        return True
    except ImportError as e:
        print(f"❌ MCP modül import hatası: {e}")
        return False
    except Exception as e:
        print(f"❌ Beklenmeyen hata: {e}")
        return False

def test_server_manager():
    """MCPServerManager sınıfının çalışabilirliğini test eder."""
    print("\n=== MCPServerManager Testi ===")
    
    try:
        from mcp.server_manager import MCPServerManager
        
        # Test config dizini
        test_config_dir = os.path.join(os.getcwd(), "test_mcp_config")
        
        # Server manager oluştur
        manager = MCPServerManager(config_dir=test_config_dir)
        print("✅ MCPServerManager başarıyla oluşturuldu")
        
        # Sunucu sayısını kontrol et
        server_count = len(manager.servers)
        print(f"✅ {server_count} sunucu konfigürasyonu yüklendi")
        
        # Test config dizinini temizle
        import shutil
        if os.path.exists(test_config_dir):
            shutil.rmtree(test_config_dir)
            
        return True
    except Exception as e:
        print(f"❌ MCPServerManager test hatası: {e}")
        import traceback
        print(f"Detay: {traceback.format_exc()}")
        return False

def test_client_manager():
    """MCPClientManager sınıfının çalışabilirliğini test eder."""
    print("\n=== MCPClientManager Testi ===")
    
    try:
        from mcp.client import MCPClientManager
        
        # Client manager oluştur
        client = MCPClientManager()
        print("✅ MCPClientManager başarıyla oluşturuldu")
        
        # MCP availability kontrolü
        is_available = client.is_mcp_available()
        print(f"✅ MCP SDK durumu: {'Mevcut' if is_available else 'Mevcut değil'}")
        
        return True
    except Exception as e:
        print(f"❌ MCPClientManager test hatası: {e}")
        import traceback
        print(f"Detay: {traceback.format_exc()}")
        return False

def test_utilities():
    """utilities.py modülündeki MCP fonksiyonlarını test eder."""
    print("\n=== Utilities MCP Testi ===")
    
    try:
        from utilities import get_mcp_manager, initialize_mcp, MCP_INTEGRATION_AVAILABLE
        
        print(f"✅ MCP entegrasyon durumu: {'Mevcut' if MCP_INTEGRATION_AVAILABLE else 'Mevcut değil'}")
        
        if MCP_INTEGRATION_AVAILABLE:
            # Manager al
            manager = get_mcp_manager()
            if manager:
                print("✅ MCP manager başarıyla alındı")
            else:
                print("⚠️  MCP manager alınamadı")
                
            # Initialize test (bu async olduğu için sadece çağırıyoruz)
            result = initialize_mcp()
            print(f"✅ MCP initialize sonucu: {result}")
        
        return True
    except Exception as e:
        print(f"❌ Utilities test hatası: {e}")
        import traceback
        print(f"Detay: {traceback.format_exc()}")
        return False

def main():
    """Ana test fonksiyonu."""
    print("BlenderGPT MCP Entegrasyon Test Scripti")
    print("=" * 50)
    
    # Test sonuçları
    results = []
    
    # Import testi
    results.append(test_mcp_imports())
    
    # Server manager testi
    results.append(test_server_manager())
    
    # Client manager testi
    results.append(test_client_manager())
    
    # Utilities testi
    results.append(test_utilities())
    
    # Sonuçları özetle
    print("\n" + "=" * 50)
    print("TEST SONUÇLARI")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Geçen testler: {passed}/{total}")
    
    if passed == total:
        print("🎉 Tüm testler başarılı! MCP entegrasyonu düzeltildi.")
        return 0
    else:
        print("⚠️  Bazı testler başarısız. Lütfen hataları kontrol edin.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
