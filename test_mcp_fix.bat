@echo off
echo BlenderGPT MCP Entegrasyon Test Scripti
echo ========================================
echo.

REM Python'un mevcut olup olmadığını kontrol et
python --version >nul 2>&1
if errorlevel 1 (
    echo HATA: Python bulunamadi. Lutfen Python'u PATH'e ekleyin.
    pause
    exit /b 1
)

echo Python bulundu.
echo.

REM Gerekli paketleri yükle
echo Gerekli paketleri yukluyor...
pip install -r requirements.txt
if errorlevel 1 (
    echo UYARI: <PERSON>et yukleme sirasinda hata olustu.
    echo Devam ediliyor...
)
echo.

REM Test scriptini çalıştır
echo MCP entegrasyon testini calistiriyor...
echo.
python test_mcp_fix.py

echo.
echo Test tamamlandi.
echo.
echo Eger testler basarisiz olduysa, lutfen MCP_FIX_README.md dosyasini inceleyin.
echo.
pause
