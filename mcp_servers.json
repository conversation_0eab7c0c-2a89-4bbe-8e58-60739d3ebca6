{"version": "1.0", "description": "MCP Server Configurations for BlenderPro", "servers": [{"name": "filesystem", "description": "Local filesystem access for Blender projects", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\temp"], "enabled": false, "auto_connect": false, "env": {}}, {"name": "git", "description": "Git repository management", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "."], "enabled": false, "auto_connect": false, "env": {}}, {"name": "brave-search", "description": "Web search for Blender tutorials and resources", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "enabled": false, "auto_connect": false, "env": {"BRAVE_API_KEY": ""}}, {"name": "sqlite", "description": "SQLite database access", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "--db-path", "database.db"], "enabled": false, "auto_connect": false, "env": {}}, {"name": "github", "description": "GitHub repository and issue management", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "enabled": false, "auto_connect": false, "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, {"name": "postgres", "description": "PostgreSQL database access", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "enabled": false, "auto_connect": false, "env": {"POSTGRES_CONNECTION_STRING": ""}}, {"name": "puppeteer", "description": "Web automation and scraping", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "enabled": false, "auto_connect": false, "env": {}}, {"name": "memory", "description": "Persistent memory for conversations", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "enabled": false, "auto_connect": false, "env": {}}, {"name": "fetch", "description": "HTTP requests and web content fetching", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "enabled": false, "auto_connect": false, "env": {}}, {"name": "everart", "description": "EverArt AI image generation", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-everart"], "enabled": false, "auto_connect": false, "env": {"EVERART_API_KEY": ""}}, {"name": "blender-local", "description": "Local Blender API server (custom)", "transport_type": "http", "url": "http://localhost:8080", "enabled": false, "auto_connect": false, "env": {}}]}