"""
Simple MCP UI Panels for BlenderPro

JSON-based MCP server configuration management with minimal UI.

Author: inkbytefo
"""

import bpy
from bpy.types import Panel, Operator

from .json_config import MCPJSONConfigManager


class BLENDPRO_OT_LoadMCPConfig(Operator):
    """Load MCP server configurations from JSON file."""
    bl_idname = "blendpro.load_mcp_config"
    bl_label = "Load MCP Config"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            config_manager = MCPJSONConfigManager()
            servers = config_manager.load_servers_from_json()
            
            # Validate config
            validation = config_manager.validate_config_file()
            if not validation["valid"]:
                error_msg = "; ".join(validation["errors"])
                self.report({'ERROR'}, f"Config validation failed: {error_msg}")
                return {'CANCELLED'}
            
            self.report({'INFO'}, f"Loaded {len(servers)} MCP servers ({validation['enabled_count']} enabled)")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to load MCP config: {e}")
            return {'CANCELLED'}


class BLENDPRO_OT_ValidateMCPConfig(Operator):
    """Validate MCP configuration file."""
    bl_idname = "blendpro.validate_mcp_config"
    bl_label = "Validate Config"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            config_manager = MCPJSONConfigManager()
            validation = config_manager.validate_config_file()
            
            if validation["valid"]:
                self.report({'INFO'}, f"Config is valid: {validation['server_count']} servers, {validation['enabled_count']} enabled")
            else:
                error_msg = "; ".join(validation["errors"])
                self.report({'ERROR'}, f"Config validation failed: {error_msg}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to validate config: {e}")
            return {'CANCELLED'}


class BLENDPRO_OT_EnableMCPServer(Operator):
    """Enable/disable an MCP server."""
    bl_idname = "blendpro.enable_mcp_server"
    bl_label = "Toggle MCP Server"
    bl_options = {'REGISTER'}
    
    server_name: bpy.props.StringProperty()
    enable: bpy.props.BoolProperty()
    
    def execute(self, context):
        try:
            config_manager = MCPJSONConfigManager()
            success = config_manager.update_server_status(self.server_name, self.enable)
            
            if success:
                status = "enabled" if self.enable else "disabled"
                self.report({'INFO'}, f"Server '{self.server_name}' {status}")
            else:
                self.report({'ERROR'}, f"Failed to update server '{self.server_name}'")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to toggle server: {e}")
            return {'CANCELLED'}


class BLENDPRO_OT_CreateDefaultMCPConfig(Operator):
    """Create default MCP configuration file."""
    bl_idname = "blendpro.create_default_mcp_config"
    bl_label = "Create Default Config"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            config_manager = MCPJSONConfigManager()
            success = config_manager.create_default_config()
            
            if success:
                self.report({'INFO'}, "Default MCP configuration created")
            else:
                self.report({'WARNING'}, "Default config already exists or creation failed")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to create default config: {e}")
            return {'CANCELLED'}


class BLENDPRO_PT_MCPSimple(Panel):
    """Simple MCP Configuration Panel."""
    bl_label = "MCP Configuration"
    bl_idname = "BLENDPRO_PT_mcp_simple"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_Panel"
    
    def draw(self, context):
        layout = self.layout
        
        # Header with main actions
        col = layout.column(align=True)
        col.operator("blendpro.load_mcp_config", text="Load Config", icon="FILE_FOLDER")
        col.operator("blendpro.validate_mcp_config", text="Validate Config", icon="CHECKMARK")
        col.operator("blendpro.create_default_mcp_config", text="Create Default", icon="FILE_NEW")
        
        layout.separator()
        
        # Show current config status
        try:
            config_manager = MCPJSONConfigManager()
            validation = config_manager.validate_config_file()
            
            box = layout.box()
            box.label(text="Configuration Status:", icon="INFO")
            
            if validation["valid"]:
                box.label(text=f"✓ Valid config with {validation['server_count']} servers")
                box.label(text=f"✓ {validation['enabled_count']} servers enabled")
            else:
                box.label(text="✗ Configuration has errors", icon="ERROR")
                for error in validation["errors"][:3]:  # Show first 3 errors
                    box.label(text=f"  • {error}")
            
            layout.separator()
            
            # Show available servers
            if validation["valid"] and validation["server_count"] > 0:
                servers = config_manager.load_servers_from_json()
                
                box = layout.box()
                box.label(text="Available Servers:", icon="NETWORK_DRIVE")
                
                for server in servers[:10]:  # Show first 10 servers
                    row = box.row()
                    
                    # Server status icon
                    if server.enabled:
                        icon = "CHECKMARK"
                        status_text = "ON"
                    else:
                        icon = "X"
                        status_text = "OFF"
                    
                    row.label(text=f"{server.name} ({status_text})", icon=icon)
                    
                    # Toggle button
                    toggle_op = row.operator("blendpro.enable_mcp_server", 
                                           text="", 
                                           icon="CHECKBOX_HLT" if server.enabled else "CHECKBOX_DEHLT")
                    toggle_op.server_name = server.name
                    toggle_op.enable = not server.enabled
                
                if len(servers) > 10:
                    box.label(text=f"... and {len(servers) - 10} more servers")
        
        except Exception as e:
            box = layout.box()
            box.label(text="Error loading config:", icon="ERROR")
            box.label(text=str(e))


class BLENDPRO_PT_MCPInfo(Panel):
    """MCP Information Panel."""
    bl_label = "MCP Information"
    bl_idname = "BLENDPRO_PT_mcp_info"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_mcp_simple"
    
    def draw(self, context):
        layout = self.layout
        
        box = layout.box()
        box.label(text="MCP Configuration File:", icon="FILE_TEXT")
        
        try:
            config_manager = MCPJSONConfigManager()
            config_file = config_manager.config_file
            
            # Show config file path
            col = box.column(align=True)
            col.label(text="Location:")
            col.label(text=config_file, icon="FOLDER_REDIRECT")
            
            # File status
            import os
            if os.path.exists(config_file):
                col.label(text="✓ File exists", icon="CHECKMARK")
                
                # File size
                file_size = os.path.getsize(config_file)
                col.label(text=f"Size: {file_size} bytes")
            else:
                col.label(text="✗ File not found", icon="ERROR")
        
        except Exception as e:
            box.label(text=f"Error: {e}", icon="ERROR")
        
        layout.separator()
        
        # Instructions
        box = layout.box()
        box.label(text="Instructions:", icon="QUESTION")
        col = box.column(align=True)
        col.label(text="1. Edit mcp_servers.json file")
        col.label(text="2. Set 'enabled': true for servers")
        col.label(text="3. Configure API keys in 'env'")
        col.label(text="4. Click 'Load Config' to apply")


# Registration
classes = [
    BLENDPRO_OT_LoadMCPConfig,
    BLENDPRO_OT_ValidateMCPConfig,
    BLENDPRO_OT_EnableMCPServer,
    BLENDPRO_OT_CreateDefaultMCPConfig,
    BLENDPRO_PT_MCPSimple,
    BLENDPRO_PT_MCPInfo,
]


def register():
    for cls in classes:
        bpy.utils.register_class(cls)


def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
