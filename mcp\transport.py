"""
MCP Transport Layer Management

Handles different transport protocols for MCP communication including
stdio, HTTP, and SSE transports.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, Optional, Tuple, Any
from enum import Enum

try:
    # Import MCP SDK from lib directory
    import sys
    import os

    # Add lib directory to path if not already there
    addon_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    lib_path = os.path.join(addon_dir, "lib")
    if lib_path not in sys.path:
        sys.path.insert(0, lib_path)

    from mcp.client.stdio import stdio_client, StdioServerParameters
    from mcp.client.streamable_http import streamablehttp_client
    MCP_AVAILABLE = True
    logging.info("MCP transport modules loaded successfully")
except ImportError as e:
    MCP_AVAILABLE = False
    logging.warning(f"MCP Python SDK not available: {e}. MCP features will be disabled.")


class TransportType(Enum):
    """Supported MCP transport types."""
    STDIO = "stdio"
    HTTP = "http"
    STREAMABLE_HTTP = "streamable_http"


class MCPTransportManager:
    """Manages MCP transport connections and protocols."""

    # Protocol version header for HTTP transport
    PROTOCOL_VERSION = "2025-06-18"

    def __init__(self):
        self.active_connections: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        self.connection_timeouts: Dict[str, float] = {}
        
    def is_mcp_available(self) -> bool:
        """Check if MCP SDK is available."""
        return MCP_AVAILABLE
    
    async def create_stdio_connection(self, 
                                    command: str, 
                                    args: list = None,
                                    env: dict = None) -> Optional[Tuple[Any, Any]]:
        """
        Create a stdio transport connection to an MCP server.
        
        Args:
            command: Command to execute the MCP server
            args: Command line arguments
            env: Environment variables
            
        Returns:
            Tuple of (read_stream, write_stream) or None if failed
        """
        if not self.is_mcp_available():
            self.logger.error("MCP SDK not available")
            return None
            
        try:
            server_params = StdioServerParameters(
                command=command,
                args=args or [],
                env=env or {}
            )
            
            connection = await stdio_client(server_params)
            connection_id = f"stdio_{command}_{len(self.active_connections)}"
            self.active_connections[connection_id] = connection
            
            self.logger.info(f"Created stdio connection: {connection_id}")
            return connection
            
        except Exception as e:
            self.logger.error(f"Failed to create stdio connection: {e}")
            return None
    
    async def create_http_connection(self, url: str, headers: Dict[str, str] = None) -> Optional[Tuple[Any, Any, Any]]:
        """
        Create an HTTP transport connection to an MCP server.

        Args:
            url: Server URL
            headers: Additional HTTP headers

        Returns:
            Tuple of (read_stream, write_stream, session) or None if failed
        """
        if not self.is_mcp_available():
            self.logger.error("MCP SDK not available")
            return None

        try:
            # Add protocol version header as per MCP spec
            default_headers = {
                "MCP-Protocol-Version": self.PROTOCOL_VERSION,
                "Accept": "application/json, text/event-stream"
            }
            if headers:
                default_headers.update(headers)

            # Validate URL format
            if not url.startswith(('http://', 'https://')):
                raise ValueError(f"Invalid URL format: {url}")

            connection = await streamablehttp_client(url, headers=default_headers)
            connection_id = f"http_{url}_{len(self.active_connections)}"
            self.active_connections[connection_id] = connection

            self.logger.info(f"Created HTTP connection: {connection_id} with protocol version {self.PROTOCOL_VERSION}")
            return connection

        except Exception as e:
            self.logger.error(f"Failed to create HTTP connection to {url}: {e}")
            return None
    
    async def close_connection(self, connection_id: str) -> bool:
        """
        Close an active MCP connection.
        
        Args:
            connection_id: ID of the connection to close
            
        Returns:
            True if successfully closed, False otherwise
        """
        if connection_id not in self.active_connections:
            self.logger.warning(f"Connection {connection_id} not found")
            return False
            
        try:
            connection = self.active_connections[connection_id]
            if hasattr(connection, 'close'):
                await connection.close()
            elif hasattr(connection, '__aexit__'):
                await connection.__aexit__(None, None, None)
                
            del self.active_connections[connection_id]
            self.logger.info(f"Closed connection: {connection_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to close connection {connection_id}: {e}")
            return False
    
    async def close_all_connections(self):
        """Close all active MCP connections."""
        connection_ids = list(self.active_connections.keys())
        for connection_id in connection_ids:
            await self.close_connection(connection_id)
    
    def get_active_connections(self) -> Dict[str, str]:
        """Get list of active connection IDs and their types."""
        return {
            conn_id: "stdio" if "stdio" in conn_id else "http" 
            for conn_id in self.active_connections.keys()
        }
