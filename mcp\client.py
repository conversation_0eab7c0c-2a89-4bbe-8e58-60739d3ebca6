"""
MCP Client Management

Main client interface for connecting to and interacting with MCP servers.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from contextlib import asynccontextmanager

try:
    # Import MCP SDK from lib directory
    import sys
    import os

    # Add lib directory to path if not already there
    addon_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    lib_path = os.path.join(addon_dir, "lib")
    if lib_path not in sys.path:
        sys.path.insert(0, lib_path)

    from mcp import ClientSession
    from mcp.types import Resource, Tool, Prompt, TextContent
    MCP_AVAILABLE = True
    logging.info("MCP Python SDK loaded successfully")
except ImportError as e:
    MCP_AVAILABLE = False
    logging.warning(f"MCP Python SDK not available: {e}. MCP features will be disabled.")

from .transport import MCPTransportManager, TransportType
from .server_manager import MCPServerManager, MCPServerConfig, ServerStatus


class MCPClientManager:
    """Main MCP client manager for BlenderPro integration."""

    # MCP Protocol Version - should match latest spec
    PROTOCOL_VERSION = "2025-06-18"

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.transport_manager = MCPTransportManager()
        self.server_manager = MCPServerManager()
        self.active_sessions: Dict[str, Any] = {}
        self.server_capabilities: Dict[str, Dict] = {}
        self.is_initialized = False
        
    def is_mcp_available(self) -> bool:
        """Check if MCP functionality is available."""
        return MCP_AVAILABLE and self.transport_manager.is_mcp_available()
    
    async def initialize(self) -> bool:
        """Initialize the MCP client manager."""
        if not self.is_mcp_available():
            self.logger.warning("MCP not available, skipping initialization")
            return False
        
        try:
            # Auto-connect to servers configured for auto-connect
            auto_connect_servers = self.server_manager.get_auto_connect_servers()
            for server in auto_connect_servers:
                await self.connect_to_server(server.name)
            
            self.is_initialized = True
            self.logger.info("MCP client manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MCP client manager: {e}")
            return False
    
    async def connect_to_server(self, server_name: str) -> bool:
        """
        Connect to an MCP server.
        
        Args:
            server_name: Name of the server to connect to
            
        Returns:
            True if connected successfully, False otherwise
        """
        if not self.is_mcp_available():
            return False
        
        server_config = self.server_manager.get_server(server_name)
        if not server_config:
            self.logger.error(f"Server {server_name} not found")
            return False
        
        if not server_config.enabled:
            self.logger.warning(f"Server {server_name} is disabled")
            return False
        
        # Check if already connected
        if server_name in self.active_sessions:
            self.logger.info(f"Already connected to {server_name}")
            return True
        
        try:
            self.server_manager.update_server_status(server_name, ServerStatus.CONNECTING)
            
            # Create transport connection based on server type
            if server_config.transport_type == "stdio":
                connection = await self.transport_manager.create_stdio_connection(
                    command=server_config.command,
                    args=server_config.args,
                    env=server_config.env
                )
                if connection:
                    read_stream, write_stream = connection
                    session = ClientSession(read_stream, write_stream)

                    # Initialize with proper capability negotiation
                    init_result = await session.initialize()

                    # Store server capabilities for later use
                    if hasattr(init_result, 'capabilities'):
                        self.server_capabilities[server_name] = init_result.capabilities
                        self.logger.info(f"Server {server_name} capabilities: {init_result.capabilities}")

                    # Validate protocol version compatibility
                    if hasattr(init_result, 'protocolVersion'):
                        server_version = init_result.protocolVersion
                        if server_version != self.PROTOCOL_VERSION:
                            self.logger.warning(f"Protocol version mismatch: client={self.PROTOCOL_VERSION}, server={server_version}")

                    self.active_sessions[server_name] = session
                    
            elif server_config.transport_type in ["http", "streamable_http"]:
                connection = await self.transport_manager.create_http_connection(
                    server_config.url
                )
                if connection:
                    read_stream, write_stream, _ = connection
                    session = ClientSession(read_stream, write_stream)
                    await session.initialize()
                    self.active_sessions[server_name] = session
            
            else:
                self.logger.error(f"Unsupported transport type: {server_config.transport_type}")
                return False
            
            if server_name in self.active_sessions:
                self.server_manager.update_server_status(server_name, ServerStatus.CONNECTED)
                self.logger.info(f"Connected to MCP server: {server_name}")
                return True
            else:
                self.server_manager.update_server_status(server_name, ServerStatus.ERROR)
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect to {server_name}: {e}")
            self.server_manager.update_server_status(server_name, ServerStatus.ERROR)
            return False
    
    async def disconnect_from_server(self, server_name: str) -> bool:
        """
        Disconnect from an MCP server.
        
        Args:
            server_name: Name of the server to disconnect from
            
        Returns:
            True if disconnected successfully, False otherwise
        """
        if server_name not in self.active_sessions:
            self.logger.warning(f"Not connected to {server_name}")
            return False
        
        try:
            session = self.active_sessions[server_name]
            if hasattr(session, 'close'):
                await session.close()
            
            del self.active_sessions[server_name]
            self.server_manager.update_server_status(server_name, ServerStatus.DISCONNECTED)
            self.logger.info(f"Disconnected from {server_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to disconnect from {server_name}: {e}")
            return False
    
    async def list_resources(self, server_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List available resources from MCP servers.
        
        Args:
            server_name: Specific server to query, or None for all connected servers
            
        Returns:
            List of resource information dictionaries
        """
        if not self.is_mcp_available():
            return []
        
        resources = []
        servers_to_query = [server_name] if server_name else list(self.active_sessions.keys())
        
        for srv_name in servers_to_query:
            if srv_name not in self.active_sessions:
                continue
                
            try:
                session = self.active_sessions[srv_name]
                response = await session.list_resources()
                
                for resource in response.resources:
                    resources.append({
                        'server': srv_name,
                        'uri': resource.uri,
                        'name': getattr(resource, 'name', ''),
                        'description': getattr(resource, 'description', ''),
                        'mimeType': getattr(resource, 'mimeType', '')
                    })
                    
            except Exception as e:
                self.logger.error(f"Failed to list resources from {srv_name}: {e}")
        
        return resources
    
    async def list_tools(self, server_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List available tools from MCP servers.
        
        Args:
            server_name: Specific server to query, or None for all connected servers
            
        Returns:
            List of tool information dictionaries
        """
        if not self.is_mcp_available():
            return []
        
        tools = []
        servers_to_query = [server_name] if server_name else list(self.active_sessions.keys())
        
        for srv_name in servers_to_query:
            if srv_name not in self.active_sessions:
                continue
                
            try:
                session = self.active_sessions[srv_name]
                response = await session.list_tools()
                
                for tool in response.tools:
                    tools.append({
                        'server': srv_name,
                        'name': tool.name,
                        'description': getattr(tool, 'description', ''),
                        'inputSchema': getattr(tool, 'inputSchema', {})
                    })
                    
            except Exception as e:
                self.logger.error(f"Failed to list tools from {srv_name}: {e}")
        
        return tools
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Call a tool on an MCP server with enhanced error handling.

        Args:
            server_name: Name of the server
            tool_name: Name of the tool to call
            arguments: Tool arguments

        Returns:
            Tool result or None if failed
        """
        if not self.is_mcp_available():
            self.logger.error("MCP not available for tool call")
            return None

        if server_name not in self.active_sessions:
            self.logger.error(f"No active session for server {server_name}")
            return None

        # Validate tool exists on server
        try:
            available_tools = await self.list_tools(server_name)
            tool_names = [tool['name'] for tool in available_tools]
            if tool_name not in tool_names:
                self.logger.error(f"Tool {tool_name} not available on server {server_name}. Available: {tool_names}")
                return {
                    'success': False,
                    'error': f"Tool {tool_name} not found",
                    'content': '',
                    'structured_content': None,
                    'is_error': True
                }
        except Exception as e:
            self.logger.warning(f"Could not validate tool availability: {e}")

        try:
            session = self.active_sessions[server_name]
            result = await session.call_tool(tool_name, arguments)

            # Extract text content from result
            content_text = ""
            if hasattr(result, 'content') and result.content:
                for content in result.content:
                    if hasattr(content, 'text'):
                        content_text += content.text + "\n"

            return {
                'success': True,
                'content': content_text.strip(),
                'structured_content': getattr(result, 'structuredContent', None),
                'is_error': getattr(result, 'isError', False),
                'tool_name': tool_name,
                'server_name': server_name
            }

        except Exception as e:
            self.logger.error(f"Failed to call tool {tool_name} on {server_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__,
                'content': '',
                'structured_content': None,
                'is_error': True,
                'tool_name': tool_name,
                'server_name': server_name
            }
    
    async def read_resource(self, server_name: str, resource_uri: str) -> Optional[str]:
        """
        Read a resource from an MCP server.
        
        Args:
            server_name: Name of the server
            resource_uri: URI of the resource to read
            
        Returns:
            Resource content or None if failed
        """
        if not self.is_mcp_available() or server_name not in self.active_sessions:
            return None
        
        try:
            session = self.active_sessions[server_name]
            from pydantic import AnyUrl
            result = await session.read_resource(AnyUrl(resource_uri))
            
            # Extract text content
            content_text = ""
            if hasattr(result, 'contents') and result.contents:
                for content in result.contents:
                    if hasattr(content, 'text'):
                        content_text += content.text + "\n"
            
            return content_text.strip()
            
        except Exception as e:
            self.logger.error(f"Failed to read resource {resource_uri} from {server_name}: {e}")
            return None
    
    def get_connected_servers(self) -> List[str]:
        """Get list of currently connected server names."""
        return list(self.active_sessions.keys())
    
    def get_server_status_summary(self) -> Dict[str, str]:
        """Get status summary of all configured servers."""
        return {
            name: self.server_manager.get_server_status(name).value
            for name in self.server_manager.servers.keys()
        }
    
    async def shutdown(self):
        """Shutdown the MCP client manager and close all connections."""
        for server_name in list(self.active_sessions.keys()):
            await self.disconnect_from_server(server_name)
        
        await self.transport_manager.close_all_connections()
        self.is_initialized = False
        self.logger.info("MCP client manager shutdown complete")
