#!/usr/bin/env python3
"""
Enhanced MCP Compliance Test Suite for BlenderPro

Tests MCP client implementation against official specification requirements.
Validates protocol compliance, transport layers, and best practices.

Author: inkbytefo
"""

import sys
import os
import asyncio
import logging
import tempfile
from typing import Dict, Any

# Add the lib directory to Python path for MCP SDK imports
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# Add the current directory to Python path for our modules
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_mcp_imports():
    """Test that MCP modules can be imported correctly."""
    print("Testing MCP imports...")

    try:
        # Import our custom MCP modules (not the SDK)
        import sys
        import os

        # Add current directory to path for our modules
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        from mcp.client import MCPClientManager
        from mcp.transport import MCPTransportManager
        from mcp.server_manager import MCPServerManager
        from mcp.config_manager import MCPConfigManager
        print("✓ All MCP modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import MCP modules: {e}")
        return False


def test_mcp_sdk_availability():
    """Test that the MCP Python SDK is available and correct version."""
    print("Testing MCP SDK availability...")

    try:
        import mcp as mcp_sdk
        from mcp import ClientSession
        from mcp.types import Resource, Tool, Prompt
        
        version = getattr(mcp_sdk, '__version__', 'unknown')
        print(f"✓ MCP SDK available (version: {version})")
        
        # Test if version is compatible (should be >= 1.11.0)
        if version != 'unknown':
            version_parts = version.split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            if major > 1 or (major == 1 and minor >= 11):
                print("✓ MCP SDK version is compatible")
            else:
                print(f"⚠ MCP SDK version {version} may be outdated (recommended: >= 1.11.0)")
        
        return True
    except ImportError as e:
        print(f"✗ MCP SDK not available: {e}")
        return False


def test_protocol_compliance():
    """Test protocol compliance features."""
    print("Testing protocol compliance...")

    try:
        # Import our custom MCP modules
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        from mcp.client import MCPClientManager
        from mcp.transport import MCPTransportManager
        
        # Test protocol version
        client_manager = MCPClientManager()
        transport_manager = MCPTransportManager()
        
        expected_version = "2025-06-18"
        
        if hasattr(client_manager, 'PROTOCOL_VERSION'):
            client_version = client_manager.PROTOCOL_VERSION
            print(f"  Client protocol version: {client_version}")
            if client_version == expected_version:
                print("✓ Client protocol version is current")
            else:
                print(f"⚠ Client protocol version mismatch (expected: {expected_version})")
        else:
            print("✗ Client missing protocol version")
            return False
        
        if hasattr(transport_manager, 'PROTOCOL_VERSION'):
            transport_version = transport_manager.PROTOCOL_VERSION
            print(f"  Transport protocol version: {transport_version}")
            if transport_version == expected_version:
                print("✓ Transport protocol version is current")
            else:
                print(f"⚠ Transport protocol version mismatch (expected: {expected_version})")
        else:
            print("✗ Transport missing protocol version")
            return False
        
        print("✓ Protocol compliance checks passed")
        return True
        
    except Exception as e:
        print(f"✗ Protocol compliance test failed: {e}")
        return False


def test_transport_layer():
    """Test transport layer functionality."""
    print("Testing transport layer...")
    
    try:
        from mcp.transport import MCPTransportManager, TransportType
        
        manager = MCPTransportManager()
        
        # Test MCP availability check
        is_available = manager.is_mcp_available()
        print(f"  MCP SDK available: {is_available}")
        
        # Test transport types
        if hasattr(TransportType, 'STDIO'):
            print("✓ STDIO transport type available")
        else:
            print("✗ STDIO transport type missing")
            return False
        
        if hasattr(TransportType, 'HTTP') or hasattr(TransportType, 'STREAMABLE_HTTP'):
            print("✓ HTTP transport types available")
        else:
            print("✗ HTTP transport types missing")
            return False
        
        # Test connection tracking
        connections = manager.get_active_connections()
        print(f"  Active connections: {len(connections)}")
        
        print("✓ Transport layer functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Transport layer test failed: {e}")
        return False


def test_server_management():
    """Test server management functionality."""
    print("Testing server management...")
    
    try:
        from mcp.server_manager import MCPServerManager, MCPServerConfig, ServerStatus
        
        # Create a temporary config directory
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MCPServerManager(config_dir=temp_dir)
            
            # Test server status enum
            statuses = [ServerStatus.DISCONNECTED, ServerStatus.CONNECTING, 
                       ServerStatus.CONNECTED, ServerStatus.ERROR]
            print(f"  Available server statuses: {[s.value for s in statuses]}")
            
            # Test adding a server
            test_server = MCPServerConfig(
                name="test-server",
                description="Test server for compliance validation",
                transport_type="stdio",
                command="echo",
                args=["hello"],
                enabled=False
            )
            
            success = manager.add_server(test_server)
            if not success:
                print("✗ Failed to add test server")
                return False
            
            # Test server validation
            issues = []
            if hasattr(manager, 'validate_server_config'):
                # This would be from our config manager
                pass
            
            # Test listing servers
            servers = manager.list_servers()
            if len(servers) == 0:
                print("✗ No servers found after adding")
                return False
            
            # Test enabled/auto-connect filtering
            enabled_servers = manager.get_enabled_servers()
            auto_connect_servers = manager.get_auto_connect_servers()
            print(f"  Enabled servers: {len(enabled_servers)}")
            print(f"  Auto-connect servers: {len(auto_connect_servers)}")
            
            # Test saving/loading config
            save_success = manager.save_server_configs()
            if not save_success:
                print("✗ Failed to save server configs")
                return False
            
            print("✓ Server management functionality working")
            return True
            
    except Exception as e:
        print(f"✗ Server management test failed: {e}")
        return False


def test_config_management():
    """Test configuration management functionality."""
    print("Testing configuration management...")
    
    try:
        from mcp.config_manager import MCPConfigManager, MCPConfigTemplate
        
        # Create a temporary config directory
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = MCPConfigManager(config_dir=temp_dir)
            
            # Test template loading
            templates = config_manager.load_templates()
            print(f"  Loaded templates: {len(templates)}")
            
            # Test templates by category
            categories = config_manager.get_templates_by_category()
            print(f"  Template categories: {list(categories.keys())}")
            
            # Test server creation from template
            if templates:
                template = templates[0]
                server_config = config_manager.create_server_from_template(
                    template.name,
                    name="test_from_template",
                    root_path="/tmp"
                )
                if server_config:
                    print("✓ Server created from template successfully")
                else:
                    print("✗ Failed to create server from template")
                    return False
            
            print("✓ Configuration management functionality working")
            return True
            
    except Exception as e:
        print(f"✗ Configuration management test failed: {e}")
        return False


async def test_client_manager_enhanced():
    """Test enhanced client manager functionality."""
    print("Testing enhanced client manager...")
    
    try:
        from mcp.client import MCPClientManager
        
        manager = MCPClientManager()
        
        # Test initialization
        is_available = manager.is_mcp_available()
        print(f"  MCP available: {is_available}")
        
        # Test protocol version
        if hasattr(manager, 'PROTOCOL_VERSION'):
            print(f"  Protocol version: {manager.PROTOCOL_VERSION}")
        
        # Test capability storage
        if hasattr(manager, 'server_capabilities'):
            print(f"  Server capabilities storage: ✓")
        else:
            print("✗ Server capabilities storage missing")
            return False
        
        if is_available:
            # Test initialization
            init_success = await manager.initialize()
            print(f"  Initialization: {'✓' if init_success else '✗'}")
            
            # Test server status
            status = manager.get_server_status_summary()
            print(f"  Server status summary: {status}")
            
            # Test enhanced error handling
            result = await manager.call_tool("nonexistent_server", "test_tool", {})
            if result and not result['success'] and 'error_type' in result:
                print("✓ Enhanced error handling working")
            else:
                print("⚠ Enhanced error handling may need improvement")
            
            # Test shutdown
            await manager.shutdown()
            print("  Shutdown completed")
        
        print("✓ Enhanced client manager functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Enhanced client manager test failed: {e}")
        return False


async def test_security_features():
    """Test security and best practices implementation."""
    print("Testing security features...")
    
    try:
        from mcp.transport import MCPTransportManager
        
        transport_manager = MCPTransportManager()
        
        # Test protocol version header for HTTP
        if hasattr(transport_manager, 'PROTOCOL_VERSION'):
            print("✓ Protocol version header support available")
        
        # Test connection timeout tracking
        if hasattr(transport_manager, 'connection_timeouts'):
            print("✓ Connection timeout tracking available")
        
        # Test validation in HTTP connections
        # This would test URL validation, header management, etc.
        
        print("✓ Security features implemented")
        return True
        
    except Exception as e:
        print(f"✗ Security features test failed: {e}")
        return False


async def main():
    """Run comprehensive MCP compliance test suite."""
    print("=== BlenderPro MCP Compliance Test Suite ===\n")
    print("Testing against MCP Specification 2025-06-18\n")
    
    tests = [
        ("MCP Imports", test_mcp_imports),
        ("MCP SDK Availability", test_mcp_sdk_availability),
        ("Protocol Compliance", test_protocol_compliance),
        ("Transport Layer", test_transport_layer),
        ("Server Management", test_server_management),
        ("Configuration Management", test_config_management),
        ("Enhanced Client Manager", test_client_manager_enhanced),
        ("Security Features", test_security_features),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n=== Compliance Test Summary ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        icon = "✓" if result else "✗"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    # Compliance assessment
    compliance_percentage = (passed / total) * 100
    print(f"Compliance Score: {compliance_percentage:.1f}%")
    
    if compliance_percentage >= 90:
        print("🎉 Excellent MCP compliance! Implementation follows best practices.")
    elif compliance_percentage >= 75:
        print("👍 Good MCP compliance with minor areas for improvement.")
    elif compliance_percentage >= 50:
        print("⚠️ Moderate compliance. Several areas need attention.")
    else:
        print("❌ Low compliance. Significant improvements needed.")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
