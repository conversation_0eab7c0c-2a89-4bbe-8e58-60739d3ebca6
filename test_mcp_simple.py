#!/usr/bin/env python3
"""
Simple MCP Test for BlenderPro

Quick test to verify MCP modules can be imported and basic functionality works.

Author: inkbytefo
"""

import sys
import os

# Add the lib directory to Python path for MCP SDK imports
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# Add the current directory to Python path for our modules
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_mcp_sdk():
    """Test MCP SDK availability."""
    print("Testing MCP SDK...")
    try:
        import mcp
        from mcp import ClientSession
        from mcp.types import Resource, Tool, Prompt
        print(f"✓ MCP SDK imported successfully")
        print(f"  Version: {getattr(mcp, '__version__', 'unknown')}")
        return True
    except ImportError as e:
        print(f"✗ MCP SDK import failed: {e}")
        return False

def test_our_modules():
    """Test our MCP modules."""
    print("Testing our MCP modules...")
    try:
        # Import our modules with specific path to avoid conflicts
        import importlib.util
        import os

        # Load our client module
        client_path = os.path.join(current_dir, 'mcp', 'client.py')
        spec = importlib.util.spec_from_file_location("blendpro_mcp_client", client_path)
        client_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(client_module)
        MCPClientManager = client_module.MCPClientManager

        # Load our transport module
        transport_path = os.path.join(current_dir, 'mcp', 'transport.py')
        spec = importlib.util.spec_from_file_location("blendpro_mcp_transport", transport_path)
        transport_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(transport_module)
        MCPTransportManager = transport_module.MCPTransportManager

        # Load our server manager module
        server_path = os.path.join(current_dir, 'mcp', 'server_manager.py')
        spec = importlib.util.spec_from_file_location("blendpro_mcp_server", server_path)
        server_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(server_module)
        MCPServerManager = server_module.MCPServerManager
        print("✓ Our MCP modules imported successfully")
        
        # Test basic instantiation
        client_manager = MCPClientManager()
        transport_manager = MCPTransportManager()
        server_manager = MCPServerManager()
        
        print("✓ MCP managers instantiated successfully")
        
        # Test MCP availability check
        is_available = client_manager.is_mcp_available()
        print(f"  MCP available: {is_available}")
        
        return True
    except Exception as e:
        print(f"✗ Our MCP modules test failed: {e}")
        return False

def test_json_config():
    """Test JSON configuration manager."""
    print("Testing JSON configuration manager...")
    try:
        # Load our JSON config manager module
        import importlib.util
        import os

        config_path = os.path.join(current_dir, 'mcp', 'json_config.py')
        spec = importlib.util.spec_from_file_location("blendpro_mcp_json_config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        MCPJSONConfigManager = config_module.MCPJSONConfigManager
        
        # Test with default config file
        config_manager = MCPJSONConfigManager()

        # Test validation
        validation = config_manager.validate_config_file()
        print(f"  Config validation: {validation['valid']}")
        print(f"  Server count: {validation['server_count']}")
        print(f"  Enabled count: {validation['enabled_count']}")

        # Test loading servers
        servers = config_manager.load_servers_from_json()
        print(f"  Loaded {len(servers)} servers from JSON")

        print("✓ JSON configuration manager working")
        return True
    except Exception as e:
        print(f"✗ Configuration manager test failed: {e}")
        return False

def main():
    """Run simple MCP tests."""
    print("=== Simple MCP Test for BlenderPro ===\n")
    
    tests = [
        ("MCP SDK", test_mcp_sdk),
        ("Our MCP Modules", test_our_modules),
        ("JSON Configuration Manager", test_json_config),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("=== Test Summary ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        icon = "✓" if result else "✗"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! MCP integration is working.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
