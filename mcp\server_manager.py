"""
MCP Server Discovery and Management

Handles discovery, configuration, and management of available MCP servers.

Author: inkbytefo
"""

import json
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


class ServerStatus(Enum):
    """MCP Server connection status."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


@dataclass
class MCPServerConfig:
    """Configuration for an MCP server."""
    name: str
    description: str
    transport_type: str  # "stdio", "http", "streamable_http"
    command: Optional[str] = None  # For stdio transport
    args: Optional[List[str]] = None  # For stdio transport
    url: Optional[str] = None  # For HTTP transport
    env: Optional[Dict[str, str]] = None  # Environment variables
    enabled: bool = True
    auto_connect: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPServerConfig':
        """Create from dictionary."""
        return cls(**data)


class MCPServerManager:
    """Manages MCP server configurations and discovery."""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config_dir = config_dir or self._get_default_config_dir()
        self.config_file = os.path.join(self.config_dir, "mcp_servers.json")
        self.servers: Dict[str, MCPServerConfig] = {}
        self.server_status: Dict[str, ServerStatus] = {}
        
        # Ensure config directory exists
        os.makedirs(self.config_dir, exist_ok=True)
        
        # Load existing configurations from JSON
        if not self.load_server_configs():
            # If loading fails, add default servers
            self._add_default_servers()
    
    def _get_default_config_dir(self) -> str:
        """Get default configuration directory."""
        try:
            import bpy
            user_data_dir = bpy.utils.user_resource('DATAFILES')
            return os.path.join(user_data_dir, "blenderpro", "mcp")
        except ImportError:
            # Fallback for non-Blender environments
            return os.path.expanduser("~/.blenderpro/mcp")
    
    def _add_default_servers(self):
        """Add some default MCP server configurations with Blender-specific servers."""
        default_servers = [
            MCPServerConfig(
                name="filesystem",
                description="Local filesystem access for Blender projects",
                transport_type="stdio",
                command="npx",
                args=["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
                enabled=False,
                auto_connect=False
            ),
            MCPServerConfig(
                name="git",
                description="Git repository management for Blender projects",
                transport_type="stdio",
                command="npx",
                args=["-y", "@modelcontextprotocol/server-git", "--repository", "."],
                enabled=False,
                auto_connect=False
            ),
            MCPServerConfig(
                name="brave-search",
                description="Web search for Blender tutorials and resources",
                transport_type="stdio",
                command="npx",
                args=["-y", "@modelcontextprotocol/server-brave-search"],
                env={"BRAVE_API_KEY": ""},
                enabled=False,
                auto_connect=False
            ),
            MCPServerConfig(
                name="blender-docs",
                description="Blender documentation and API reference",
                transport_type="stdio",
                command="python",
                args=["-m", "blender_docs_server"],  # Hypothetical Blender docs server
                enabled=False,
                auto_connect=False
            )
        ]
        
        for server in default_servers:
            self.add_server(server)
        
        self.save_server_configs()
    
    def add_server(self, server_config: MCPServerConfig) -> bool:
        """
        Add a new MCP server configuration.
        
        Args:
            server_config: Server configuration to add
            
        Returns:
            True if added successfully, False if name already exists
        """
        if server_config.name in self.servers:
            self.logger.warning(f"Server {server_config.name} already exists")
            return False
        
        self.servers[server_config.name] = server_config
        self.server_status[server_config.name] = ServerStatus.DISCONNECTED
        self.logger.info(f"Added MCP server: {server_config.name}")
        return True
    
    def remove_server(self, server_name: str) -> bool:
        """
        Remove an MCP server configuration.
        
        Args:
            server_name: Name of the server to remove
            
        Returns:
            True if removed successfully, False if not found
        """
        if server_name not in self.servers:
            self.logger.warning(f"Server {server_name} not found")
            return False
        
        del self.servers[server_name]
        if server_name in self.server_status:
            del self.server_status[server_name]
        
        self.logger.info(f"Removed MCP server: {server_name}")
        return True
    
    def get_server(self, server_name: str) -> Optional[MCPServerConfig]:
        """Get server configuration by name."""
        return self.servers.get(server_name)
    
    def list_servers(self) -> List[MCPServerConfig]:
        """Get list of all configured servers."""
        return list(self.servers.values())
    
    def get_enabled_servers(self) -> List[MCPServerConfig]:
        """Get list of enabled servers."""
        return [server for server in self.servers.values() if server.enabled]
    
    def get_auto_connect_servers(self) -> List[MCPServerConfig]:
        """Get list of servers configured for auto-connect."""
        return [server for server in self.servers.values() 
                if server.enabled and server.auto_connect]
    
    def update_server_status(self, server_name: str, status: ServerStatus):
        """Update the connection status of a server."""
        if server_name in self.servers:
            self.server_status[server_name] = status
            self.logger.debug(f"Server {server_name} status: {status.value}")
    
    def get_server_status(self, server_name: str) -> ServerStatus:
        """Get the connection status of a server."""
        return self.server_status.get(server_name, ServerStatus.DISCONNECTED)
    
    def save_server_configs(self) -> bool:
        """Save server configurations to file."""
        try:
            config_data = {
                name: server.to_dict() 
                for name, server in self.servers.items()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Saved server configs to {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save server configs: {e}")
            return False
    
    def load_server_configs(self) -> bool:
        """Load server configurations from JSON file using JSON config manager."""
        try:
            from .json_config import MCPJSONConfigManager

            json_config = MCPJSONConfigManager()
            servers = json_config.load_servers_from_json()

            self.servers = {}
            self.server_status = {}

            for server in servers:
                self.servers[server.name] = server
                self.server_status[server.name] = ServerStatus.DISCONNECTED

            self.logger.info(f"Loaded {len(self.servers)} server configurations from JSON")

            # If no servers were loaded, add defaults
            if len(self.servers) == 0:
                self.logger.info("No servers found in config, adding default servers")
                self._add_default_servers()

            return True

        except ImportError as e:
            self.logger.error(f"Failed to import JSON config manager: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Failed to load server configs from JSON: {e}")
            return False
