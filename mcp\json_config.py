"""
Simple JSON-based MCP Server Configuration Manager

Loads MCP server configurations from standard JSON files.

Author: inkbytefo
"""

import json
import os
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

from .server_manager import MCPServerConfig, MCPServerManager


class MCPJSONConfigManager:
    """Simple JSON-based configuration manager for MCP servers."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Default config file path
        if config_file is None:
            addon_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.config_file = os.path.join(addon_dir, "mcp_servers.json")
        else:
            self.config_file = config_file
        
        self.logger.info(f"Using MCP config file: {self.config_file}")
    
    def load_servers_from_json(self) -> List[MCPServerConfig]:
        """Load MCP server configurations from JSON file."""
        if not os.path.exists(self.config_file):
            self.logger.warning(f"MCP config file not found: {self.config_file}")
            return []
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            servers = []
            server_configs = data.get("servers", [])
            
            for server_data in server_configs:
                try:
                    # Create server config from JSON data
                    server = MCPServerConfig(
                        name=server_data.get("name", ""),
                        description=server_data.get("description", ""),
                        transport_type=server_data.get("transport_type", "stdio"),
                        command=server_data.get("command"),
                        args=server_data.get("args"),
                        url=server_data.get("url"),
                        env=server_data.get("env", {}),
                        enabled=server_data.get("enabled", False),
                        auto_connect=server_data.get("auto_connect", False)
                    )
                    servers.append(server)
                    
                except Exception as e:
                    self.logger.error(f"Failed to parse server config {server_data.get('name', 'unknown')}: {e}")
                    continue
            
            self.logger.info(f"Loaded {len(servers)} MCP server configurations from JSON")
            return servers
            
        except Exception as e:
            self.logger.error(f"Failed to load MCP config from {self.config_file}: {e}")
            return []
    
    def save_servers_to_json(self, servers: List[MCPServerConfig]) -> bool:
        """Save MCP server configurations to JSON file."""
        try:
            # Create the data structure
            data = {
                "version": "1.0",
                "description": "MCP Server Configurations for BlenderPro",
                "servers": []
            }
            
            for server in servers:
                server_data = {
                    "name": server.name,
                    "description": server.description,
                    "transport_type": server.transport_type,
                    "enabled": server.enabled,
                    "auto_connect": server.auto_connect,
                    "env": server.env or {}
                }
                
                # Add transport-specific fields
                if server.transport_type == "stdio":
                    server_data["command"] = server.command
                    server_data["args"] = server.args or []
                elif server.transport_type in ["http", "streamable_http"]:
                    server_data["url"] = server.url
                
                data["servers"].append(server_data)
            
            # Save to file
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Saved {len(servers)} MCP server configurations to JSON")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save MCP config to {self.config_file}: {e}")
            return False
    
    def get_enabled_servers(self) -> List[MCPServerConfig]:
        """Get only enabled servers from JSON config."""
        all_servers = self.load_servers_from_json()
        return [server for server in all_servers if server.enabled]
    
    def get_auto_connect_servers(self) -> List[MCPServerConfig]:
        """Get servers that should auto-connect on startup."""
        all_servers = self.load_servers_from_json()
        return [server for server in all_servers if server.enabled and server.auto_connect]
    
    def update_server_status(self, server_name: str, enabled: bool, auto_connect: bool = None) -> bool:
        """Update a specific server's enabled/auto_connect status."""
        try:
            servers = self.load_servers_from_json()
            
            for server in servers:
                if server.name == server_name:
                    server.enabled = enabled
                    if auto_connect is not None:
                        server.auto_connect = auto_connect
                    break
            else:
                self.logger.warning(f"Server {server_name} not found in config")
                return False
            
            return self.save_servers_to_json(servers)
            
        except Exception as e:
            self.logger.error(f"Failed to update server {server_name} status: {e}")
            return False
    
    def add_custom_server(self, server_config: MCPServerConfig) -> bool:
        """Add a new custom server to the JSON config."""
        try:
            servers = self.load_servers_from_json()
            
            # Check if server with same name already exists
            for existing_server in servers:
                if existing_server.name == server_config.name:
                    self.logger.warning(f"Server {server_config.name} already exists")
                    return False
            
            servers.append(server_config)
            return self.save_servers_to_json(servers)
            
        except Exception as e:
            self.logger.error(f"Failed to add custom server {server_config.name}: {e}")
            return False
    
    def remove_server(self, server_name: str) -> bool:
        """Remove a server from the JSON config."""
        try:
            servers = self.load_servers_from_json()
            original_count = len(servers)
            
            servers = [server for server in servers if server.name != server_name]
            
            if len(servers) == original_count:
                self.logger.warning(f"Server {server_name} not found in config")
                return False
            
            return self.save_servers_to_json(servers)
            
        except Exception as e:
            self.logger.error(f"Failed to remove server {server_name}: {e}")
            return False
    
    def validate_config_file(self) -> Dict[str, Any]:
        """Validate the JSON config file and return validation results."""
        result = {
            "valid": False,
            "errors": [],
            "warnings": [],
            "server_count": 0,
            "enabled_count": 0
        }
        
        try:
            if not os.path.exists(self.config_file):
                result["errors"].append(f"Config file not found: {self.config_file}")
                return result
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check required fields
            if "servers" not in data:
                result["errors"].append("Missing 'servers' field in config")
                return result
            
            servers = data["servers"]
            if not isinstance(servers, list):
                result["errors"].append("'servers' field must be a list")
                return result
            
            result["server_count"] = len(servers)
            enabled_count = 0
            
            # Validate each server
            for i, server in enumerate(servers):
                if not isinstance(server, dict):
                    result["errors"].append(f"Server {i} is not a valid object")
                    continue
                
                # Check required fields
                required_fields = ["name", "transport_type"]
                for field in required_fields:
                    if field not in server:
                        result["errors"].append(f"Server {i} missing required field: {field}")
                
                # Check transport-specific requirements
                transport_type = server.get("transport_type")
                if transport_type == "stdio":
                    if "command" not in server:
                        result["errors"].append(f"Server {i} (stdio) missing 'command' field")
                elif transport_type in ["http", "streamable_http"]:
                    if "url" not in server:
                        result["errors"].append(f"Server {i} (http) missing 'url' field")
                
                # Count enabled servers
                if server.get("enabled", False):
                    enabled_count += 1
            
            result["enabled_count"] = enabled_count
            result["valid"] = len(result["errors"]) == 0
            
            if result["valid"]:
                result["warnings"].append(f"Config is valid with {result['server_count']} servers ({enabled_count} enabled)")
            
        except json.JSONDecodeError as e:
            result["errors"].append(f"Invalid JSON format: {e}")
        except Exception as e:
            result["errors"].append(f"Validation error: {e}")
        
        return result
    
    def create_default_config(self) -> bool:
        """Create a default configuration file if it doesn't exist."""
        if os.path.exists(self.config_file):
            self.logger.info("Config file already exists, skipping default creation")
            return True
        
        try:
            # Create default servers list
            default_servers = [
                MCPServerConfig(
                    name="filesystem",
                    description="Local filesystem access",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-filesystem", "C:\\temp"],
                    enabled=False,
                    auto_connect=False
                ),
                MCPServerConfig(
                    name="git",
                    description="Git repository management",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-git", "--repository", "."],
                    enabled=False,
                    auto_connect=False
                ),
                MCPServerConfig(
                    name="brave-search",
                    description="Web search capabilities",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-brave-search"],
                    env={"BRAVE_API_KEY": ""},
                    enabled=False,
                    auto_connect=False
                )
            ]
            
            success = self.save_servers_to_json(default_servers)
            if success:
                self.logger.info(f"Created default MCP config file: {self.config_file}")
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to create default config: {e}")
            return False
